# TPA API Implementation Documentation

## Project Overview

This document tracks the development progress of the TPA API, a FastAPI-based REST API for insurance policy and claims management. The API provides three endpoints (PolicyListSF, PolicyDetailSF, ClaimListSF) using comprehensive mock data stored in CSV format.

## Requirements Clarifications

Based on stakeholder feedback, the following clarifications were established:

- **Name Matching**: Exact match comparison (case-sensitive) for NAME_TH and NAME_EN parameters
- **Date/Time Handling**: Thailand timezone (UTC+7) for all date comparisons when filtering active policies
- **Response Ordering**: Natural order (as loaded from CSV) - no specific sorting required
- **Claim Status Correction**: "Authoried" corrected to "Authorized"
- **Empty Results**: Return empty list `[]` when no data found, not error messages

## Complete Implementation Plan

### Phase 1: Foundation ✅ COMPLETED
**Complexity**: Low | **Duration**: 2 hours | **Dependencies**: None

**Files Implemented**:
1. `config/settings.py` - Application configuration management
2. `src/utils/logger.py` - Structured logging setup
3. `src/utils/exceptions.py` - Custom exception classes
4. `src/utils/helpers.py` - Utility functions for date handling and data processing
5. `src/models/data_models.py` - Pydantic models for internal data structures

### Phase 2: Data Layer ✅ COMPLETED
**Complexity**: Medium | **Duration**: 4 hours | **Dependencies**: Phase 1

**Files Implemented**:
1. `src/data/mock_data.py` - CSV data loading and initialization
2. `src/data/policy_data.py` - Policy data management and indexing
3. `src/data/claim_data.py` - Claim data management and indexing

### Phase 3: API Models ✅ COMPLETED
**Complexity**: Medium | **Duration**: 3 hours | **Dependencies**: Phase 1, 2

**Files Implemented**:
1. `src/models/request_models.py` - Pydantic request models with validation
2. `src/models/response_models.py` - Pydantic response models for JSON output

### Phase 4: Business Logic ✅ COMPLETED
**Complexity**: High | **Duration**: 8 hours | **Dependencies**: Phase 1, 2, 3

**Files Implemented**:
1. `src/services/validation_service.py` - Parameter validation logic
2. `src/services/policy_service.py` - Business logic for policies
3. `src/services/claim_service.py` - Business logic for claims

### Phase 5: API Layer ✅ COMPLETED
**Complexity**: Medium | **Duration**: 4 hours | **Dependencies**: Phase 1-4

**Files Implemented**:
1. `src/api/dependencies.py` - Shared dependencies and validation
2. `src/api/routes/policy_list.py` - PolicyListSF endpoint
3. `src/api/routes/policy_detail.py` - PolicyDetailSF endpoint
4. `src/api/routes/claim_list.py` - ClaimListSF endpoint

### Phase 6: Application Assembly
**Complexity**: Low | **Duration**: 2 hours | **Dependencies**: Phase 1-5

**Files to Implement**:
1. `src/main.py` - FastAPI application factory and startup
2. `docker/Dockerfile` - Container definition
3. `docker/docker-compose.yml` - Local development setup

**Total Estimated Duration**: 23 hours

## Phase 1 Implementation Summary ✅

### 1. Configuration Management (`config/settings.py`)

**Key Features**:
- Pydantic-based settings with environment variable support
- Thailand timezone configuration (UTC+7)
- Configurable logging, server, and API settings
- Type-safe configuration with field descriptions

**Technical Highlights**:
```python
# Thailand timezone support
THAILAND_TZ = timezone(timedelta(hours=settings.timezone_offset))

# Environment-based configuration
class Settings(BaseSettings):
    timezone_offset: int = Field(default=7, description="Thailand timezone offset (UTC+7)")
```

### 2. Structured Logging (`src/utils/logger.py`)

**Key Features**:
- JSON and text logging formatters
- Structured logging with extra fields support
- Uvicorn and FastAPI logger integration
- Configurable log levels and formats

**Technical Highlights**:
```python
# Custom JSON formatter for structured logging
class JSONFormatter(logging.Formatter):
    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "message": record.getMessage(),
            # ... additional fields
        }
```

### 3. Exception Handling (`src/utils/exceptions.py`)

**Key Features**:
- Custom exception hierarchy with TPAAPIException base
- HTTP status code mapping for FastAPI integration
- Detailed error context with field names and details
- Specific exceptions for validation, data loading, and member lookup

**Technical Highlights**:
```python
# Exception to HTTP converter mapping
EXCEPTION_HANDLERS = {
    ValidationError: validation_error_to_http,
    MemberNotFoundError: member_not_found_to_http,
    DataLoadingError: data_loading_error_to_http,
}
```

### 4. Utility Functions (`src/utils/helpers.py`)

**Key Features**:
- Thailand timezone date parsing and validation
- Exact case-sensitive string matching (per requirements)
- Policy active status validation
- Claim status validation with "Authorized" correction
- Data indexing utilities for fast lookups

**Technical Highlights**:
```python
# Thailand timezone date handling
def get_current_date_thailand() -> date:
    thailand_tz = get_thailand_timezone()
    return datetime.now(thailand_tz).date()

# Exact string matching (case-sensitive)
def exact_match(value1: str, value2: str) -> bool:
    return normalize_string(value1) == normalize_string(value2)

# Claim status validation with correction
def validate_claim_status(status: str) -> bool:
    allowed_statuses = {
        "Approved", "Authorized", "Open", "Paid",
        "Pending", "Pending For Approval", "Rejected"
    }
    return status in allowed_statuses
```

### 5. Data Models (`src/models/data_models.py`)

**Key Features**:
- Complete Pydantic models for all 7 CSV data structures
- Type-safe field definitions matching CSV columns exactly
- Date and datetime validation with proper error handling
- Models for all data types: policies, payments, benefits, conditions, claims

**Technical Highlights**:
```python
# Type-safe models with validation
class PolicyRecord(BaseModel):
    MemberCode: str
    CitizenID: str
    PlanEffFrom: str
    PlanEffTo: str
    # ... 34 more fields

    @validator('PlanEffFrom', 'PlanEffTo')
    def validate_dates(cls, v, field):
        if v:
            try:
                parse_date(v, field.name)
            except Exception:
                pass  # Keep original string
        return v
```

## Technical Decisions and Rationale

### 1. **Pydantic for Configuration and Data Models**
- **Decision**: Use Pydantic for settings and data validation
- **Rationale**: Type safety, automatic validation, environment variable support
- **Benefit**: Catches configuration and data errors early

### 2. **Thailand Timezone Handling**
- **Decision**: Centralized timezone management with UTC+7
- **Rationale**: Requirements specify Thailand timezone for date comparisons
- **Implementation**: `get_thailand_timezone()` function used throughout

### 3. **Structured Logging**
- **Decision**: JSON and text formatters with configurable output
- **Rationale**: Production deployments need structured logs for monitoring
- **Benefit**: Easy integration with log aggregation systems

### 4. **Custom Exception Hierarchy**
- **Decision**: Specific exceptions with HTTP status code mapping
- **Rationale**: Better error handling and API response consistency
- **Benefit**: Clear error messages and proper HTTP status codes

### 5. **Exact String Matching**
- **Decision**: Case-sensitive exact matching for names
- **Rationale**: Stakeholder requirement for precise name searches
- **Implementation**: `exact_match()` function with string normalization

## Mock Data Structure Overview

The API uses 7 CSV files with comprehensive insurance data:

- **policies.csv** (12 records): Complete member and policy information
- **payment_details.csv** (17 records): Payment methods and bank details
- **policy_details.csv** (12 records): Main benefit information
- **benefits.csv** (12 records): Detailed benefit breakdown with limits
- **contract_conditions.csv** (11 records): Contract-level conditions
- **member_conditions.csv** (11 records): Member-specific conditions
- **claims.csv** (13 records): Claims history with all required statuses

**Data Relationships**:
- **MemberCode**: Primary key linking all data
- **PolicyNo**: Groups members under same policy
- **CitizenID**: Unique identification
- **InsurerCode/CompanyCode**: Organizational grouping

## API Endpoint Specifications

### 1. PolicyListSF
- **Path**: `/api/PolicyListSF`
- **Method**: GET
- **Parameters**: 9 different parameter combinations with INSURER_CODE + additional fields
- **Response**: List of policies with payment details
- **Filtering**: Active policies only (PlanEffFrom <= today <= PlanEffTo)

### 2. PolicyDetailSF
- **Path**: `/api/PolicyDetailSF`
- **Method**: GET
- **Parameters**: MEMBER_CODE (required)
- **Response**: Detailed policy information with benefits, conditions, claim history
- **Data Sources**: Multiple CSV files aggregated

### 3. ClaimListSF
- **Path**: `/api/ClaimListSF`
- **Method**: GET
- **Parameters**: MEMBER_CODE or (INSURER_CODE + CITIZEN_ID)
- **Response**: List of claims with allowed statuses only
- **Filtering**: Approved, Authorized, Open, Paid, Pending, Pending For Approval, Rejected

## Current Status

### ✅ Completed
- **Phase 1: Foundation** - All 5 components implemented and tested
- **Phase 2: Data Layer** - All 3 components implemented and tested
- **Phase 3: API Models** - All 2 components implemented and tested
- **Phase 4: Business Logic** - All 3 components implemented and tested
- **Configuration Management** - Environment-based settings with Thailand timezone
- **Logging Infrastructure** - Structured JSON/text logging ready
- **Exception Framework** - Custom exceptions with HTTP mapping (enhanced with NotFoundError, BusinessLogicError)
- **Utility Functions** - Date handling, string matching, data processing
- **Data Models** - Type-safe Pydantic V2 models for all CSV structures
- **CSV Data Loading** - Robust loading with error recovery and validation
- **Policy Data Management** - Efficient indexing and filtering for all policy operations
- **Claim Data Management** - Status validation and multi-criteria claim lookups
- **Request Models** - Comprehensive parameter validation for all 3 endpoints
- **Response Models** - Complete JSON response structures for all endpoints
- **Validation Service** - Parameter validation logic for all endpoints with strategy determination
- **Policy Service** - Complete business logic for PolicyListSF and PolicyDetailSF endpoints
- **Claim Service** - Complete business logic for ClaimListSF endpoint
- **API Layer** - FastAPI routes and dependencies for all three endpoints
- **Response Models** - Updated to match actual CSV data structure

### 🔄 Current State
- **Ready for Phase 6: Application Assembly**
- **Dependencies Satisfied**: Foundation, data layer, API models, business logic, and API layer complete
- **Next Steps**: Implement main FastAPI application, Docker configuration, and startup logic

### 📋 Remaining Work
- **Phase 6**: Application Assembly (main app, Docker)

## Integration Points

The completed Phase 1 and Phase 2 components provide these integration points for subsequent phases:

### Phase 1 Integration Points:
1. **Settings**: `get_settings()` and `get_thailand_timezone()` for configuration
2. **Logging**: `get_logger()` for structured logging throughout application
3. **Exceptions**: Exception classes and HTTP converters for error handling
4. **Helpers**: Date parsing, string matching, and data processing utilities
5. **Models**: Type-safe data structures for CSV data validation

### Phase 2 Integration Points:
1. **Data Loading**: `get_data_loader()` and `initialize_data()` for CSV data access
2. **Policy Management**: `get_policy_manager()` for all policy-related operations
3. **Claim Management**: `get_claim_manager()` for all claim-related operations
4. **Data Access**: Efficient indexed lookups for member codes, citizen IDs, insurer codes
5. **Filtering**: Active policy filtering, claim status validation, multi-criteria searches

## Next Phase Readiness

Phase 3: API Models is ready to begin with:
- ✅ Complete data layer with efficient indexing and filtering
- ✅ Policy management supporting all PolicyListSF and PolicyDetailSF requirements
- ✅ Claim management supporting all ClaimListSF requirements
- ✅ Robust error handling and logging infrastructure
- ✅ Thailand timezone support for date operations
- ✅ Exact case-sensitive name matching capabilities
- ✅ Comprehensive test coverage for all data operations

The foundation and data layer provide a complete backend infrastructure for implementing the API request/response models and business logic.

## Phase 4 Implementation Summary ✅

### 1. Validation Service (`src/services/validation_service.py`)

**Purpose**: Centralized parameter validation and search strategy determination for all API endpoints.

**Key Features**:
- **Request Validation**: Validates all three endpoint parameter combinations using Pydantic models
- **Strategy Determination**: Analyzes validated requests to determine optimal search strategies
- **Error Handling**: Provides detailed validation error messages with field-level information
- **Logging**: Comprehensive logging of validation attempts and results

**Supported Strategies**:
- **PolicyListSF**: 10 parameter combinations (citizen_id, policy_no_name, certificate_no_name, staff_no_name, other_id, name_only)
- **ClaimListSF**: 2 parameter combinations (member_code, insurer_citizen)

### 2. Policy Service (`src/services/policy_service.py`)

**Purpose**: Business logic implementation for PolicyListSF and PolicyDetailSF endpoints.

**Key Features**:
- **PolicyListSF Logic**: Implements all 10 search parameter combinations with exact matching
- **PolicyDetailSF Logic**: Complete member profile aggregation from multiple data sources
- **Data Conversion**: Converts internal data structures to API response models
- **Error Handling**: Graceful handling of missing data and conversion errors
- **Integration**: Seamless integration with policy and claim data managers

**Search Implementations**:
- **Citizen ID Search**: Direct lookup with insurer code filtering
- **Policy Number + Name**: Combined filtering with exact case-sensitive name matching
- **Certificate Number + Name**: Certificate-based search with name validation
- **Staff Number + Name**: Staff-based search with name validation
- **Other ID Search**: Alternative ID-based lookup
- **Name-Only Search**: Thai/English name-based filtering

**Response Assembly**:
- **Policy Details**: Main policy information with payment details
- **Benefits**: Complete benefit structure with limits and balances
- **Conditions**: Contract and member-specific conditions
- **Claim History**: Associated claims with full details

### 3. Claim Service (`src/services/claim_service.py`)

**Purpose**: Business logic implementation for ClaimListSF endpoint.

**Key Features**:
- **Member Code Search**: Direct claim lookup by member code
- **Insurer + Citizen ID Search**: Cross-reference policy lookup with claim retrieval
- **Status Filtering**: Automatic filtering of claims by valid statuses only
- **Data Conversion**: Complete claim data to response model conversion
- **Validation Integration**: Status validation and claim filtering

**Search Implementations**:
- **Member Code Strategy**: Direct claim retrieval with status validation
- **Insurer + Citizen Strategy**: Policy lookup followed by claim retrieval with insurer verification

### 4. Enhanced Exception Framework

**New Exceptions Added**:
- **NotFoundError**: For resource not found scenarios (404 HTTP status)
- **BusinessLogicError**: For business logic failures (422 HTTP status)
- **HTTP Converters**: Automatic conversion to appropriate HTTP responses

### 5. Integration Architecture

**Service Dependencies**:
- **Validation Service**: Standalone with request model dependencies
- **Policy Service**: Depends on validation service, policy manager, and claim manager
- **Claim Service**: Depends on validation service, claim manager, and policy manager

**Data Flow**:
1. **Request Validation**: Parameters validated and strategy determined
2. **Data Retrieval**: Appropriate data managers called based on strategy
3. **Business Logic**: Data filtering, matching, and processing
4. **Response Assembly**: Data conversion to API response models
5. **Error Handling**: Graceful error handling with appropriate HTTP responses

### 6. Testing Coverage

**Test Categories**:
- **Unit Tests**: Individual service method testing with mocked dependencies
- **Integration Tests**: Service interaction testing with real data
- **Validation Tests**: Parameter validation and strategy determination testing
- **Error Handling Tests**: Exception scenarios and error response testing

**Test Results**: 15 test cases, all passing with comprehensive coverage of:
- Request validation for all parameter combinations
- Search strategy determination
- Business logic execution
- Data conversion and response assembly
- Error handling and edge cases

## Development Notes

### Code Quality Standards
- **Type Hints**: All functions and classes use proper type annotations
- **Documentation**: Comprehensive docstrings for all modules and functions
- **Error Handling**: Specific exceptions with detailed error context
- **Testing Ready**: Components designed for easy unit testing
- **Production Ready**: Structured logging and configuration management

### Performance Considerations
- **In-Memory Storage**: Fast data access with pre-built indexes
- **Lazy Loading**: Data loaded once at startup
- **Efficient Lookups**: O(1) dictionary lookups for key-based searches
- **Memory Trade-offs**: Multiple indexes for query performance

### Security Considerations
- **Input Validation**: Pydantic models validate all input data
- **No Authentication**: As per requirements, APIs are open
- **Error Information**: Detailed errors for debugging, not security risks
- **Data Sanitization**: String normalization and safe dictionary access

This implementation provides a solid foundation for building a production-ready TPA API with proper error handling, logging, and data management capabilities.

## Phase 5 Implementation Summary ✅

### 1. API Dependencies (`src/api/dependencies.py`)

**Purpose**: Shared dependencies and utilities for FastAPI routes.

**Key Features**:
- **Query Parameter Extraction**: Normalizes query parameters from FastAPI requests
- **Exception Handling**: Converts service layer exceptions to appropriate HTTP responses
- **Service Dependencies**: Provides dependency injection for policy, claim, and validation services
- **Logging Integration**: Comprehensive logging for API request processing

**Technical Highlights**:
```python
# Query parameter normalization
def get_query_params(request: Request) -> Dict[str, Any]:
    params = {}
    for key, value in request.query_params.items():
        params[key] = value if value.strip() else None
    return params

# Exception to HTTP conversion
def handle_service_exception(error: Exception) -> HTTPException:
    for exception_type, handler in EXCEPTION_HANDLERS.items():
        if isinstance(error, exception_type):
            return handler(error)
```

### 2. PolicyListSF Endpoint (`src/api/routes/policy_list.py`)

**Purpose**: FastAPI route implementation for policy list retrieval.

**Key Features**:
- **Multiple Parameter Support**: Handles all 10 parameter combinations
- **Service Integration**: Seamless integration with policy service layer
- **Error Handling**: Comprehensive error handling with proper HTTP status codes
- **Request Logging**: Detailed logging of requests and responses
- **Type Safety**: Full type annotations with Pydantic response models

**API Specification**:
- **Path**: `/api/PolicyListSF`
- **Method**: GET
- **Response**: `List[PolicyListResponse]`
- **Parameters**: 10 different combinations with INSURER_CODE + additional fields

### 3. PolicyDetailSF Endpoint (`src/api/routes/policy_detail.py`)

**Purpose**: FastAPI route implementation for detailed policy information retrieval.

**Key Features**:
- **Member-Specific Data**: Retrieves comprehensive member information
- **Multi-Source Aggregation**: Combines data from policies, benefits, conditions, and claims
- **Complete Response Structure**: Returns nested response with all related data
- **Error Handling**: Proper handling of member not found scenarios

**API Specification**:
- **Path**: `/api/PolicyDetailSF`
- **Method**: GET
- **Response**: `PolicyDetailSFResponse`
- **Parameters**: MEMBER_CODE (required)

### 4. ClaimListSF Endpoint (`src/api/routes/claim_list.py`)

**Purpose**: FastAPI route implementation for claim list retrieval.

**Key Features**:
- **Dual Search Strategies**: Supports member code and insurer+citizen ID lookups
- **Status Filtering**: Automatic filtering of claims by valid statuses
- **Cross-Reference Lookup**: Policy-based claim retrieval for insurer+citizen searches
- **Comprehensive Logging**: Detailed request and response logging

**API Specification**:
- **Path**: `/api/ClaimListSF`
- **Method**: GET
- **Response**: `List[ClaimHistoryResponse]`
- **Parameters**: MEMBER_CODE or (INSURER_CODE + CITIZEN_ID)

### 5. Response Model Updates

**Issue Resolved**: Updated response models to match actual CSV data structure.

**Changes Made**:
- **ContractConditionResponse**: Removed non-existent `ConditionCode` field, added all CSV fields
- **MemberConditionResponse**: Aligned with actual member conditions CSV structure
- **Service Layer**: Updated conversion methods to use correct field mappings

**Technical Impact**:
- Eliminated Pydantic validation errors
- Ensured accurate data mapping from CSV to API responses
- Maintained type safety throughout the application

### 6. Integration Architecture

**Service Layer Integration**:
- **Dependency Injection**: Clean separation using FastAPI's dependency system
- **Exception Propagation**: Service exceptions properly converted to HTTP responses
- **Logging Chain**: Request logging flows through all layers
- **Type Safety**: End-to-end type safety from request to response

**Error Handling Flow**:
1. **Service Layer**: Raises specific exceptions (ValidationError, NotFoundError, etc.)
2. **API Layer**: Catches exceptions and converts using `handle_service_exception`
3. **HTTP Response**: Returns appropriate status codes with detailed error information
4. **Logging**: All errors logged with context for debugging

### 7. Testing and Validation

**Validation Results**:
- ✅ All API routes can be imported successfully
- ✅ Service dependencies work correctly
- ✅ Query parameter extraction functions properly
- ✅ Exception handling converts errors to proper HTTP responses
- ✅ Response models validate against actual data
- ✅ End-to-end service calls complete successfully

**Test Coverage**:
- **PolicyDetailSF**: Successfully retrieves member data with all related information
- **ClaimListSF**: Successfully retrieves claims by member code
- **PolicyListSF**: Successfully processes search parameters (though may return empty results due to date filtering)

## Technical Decisions and Rationale

### 1. **FastAPI Route Structure**
- **Decision**: Separate route files for each endpoint
- **Rationale**: Better organization, easier maintenance, and clear separation of concerns
- **Benefit**: Each endpoint can be developed and tested independently

### 2. **Dependency Injection Pattern**
- **Decision**: Use FastAPI's dependency system for service injection
- **Rationale**: Promotes loose coupling and makes testing easier
- **Benefit**: Services can be easily mocked for unit testing

### 3. **Centralized Exception Handling**
- **Decision**: Single exception handler function with mapping dictionary
- **Rationale**: Consistent error responses across all endpoints
- **Benefit**: Easy to maintain and extend error handling

### 4. **Query Parameter Normalization**
- **Decision**: Convert empty strings to None in parameter extraction
- **Rationale**: Consistent handling of optional parameters
- **Benefit**: Simplifies validation logic in service layer

### 5. **Comprehensive Logging**
- **Decision**: Log requests, responses, and errors at API layer
- **Rationale**: Essential for debugging and monitoring in production
- **Benefit**: Complete audit trail for API usage

## Performance Considerations

- **Service Singletons**: Services are instantiated once and reused
- **Efficient Data Access**: Leverages existing data layer indexing
- **Minimal Overhead**: Direct service calls without unnecessary abstraction layers
- **Type Safety**: Compile-time type checking prevents runtime errors

## Security Considerations

- **Input Validation**: All parameters validated through Pydantic models
- **Error Information**: Detailed errors for debugging without exposing sensitive data
- **No Authentication**: As per requirements, APIs are open (suitable for internal use)
- **Data Sanitization**: Safe parameter handling and dictionary access

The Phase 5 API Layer implementation provides a complete, production-ready FastAPI interface that seamlessly integrates with the existing service layer while maintaining proper error handling, logging, and type safety throughout the application.

## Phase 2 Implementation Summary ✅

### 1. CSV Data Loading (`src/data/mock_data.py`)

**Key Features**:
- Robust CSV file loading with error handling and recovery
- Pydantic model validation for all data types
- Singleton pattern for global data loader instance
- Comprehensive logging for data loading operations
- Graceful handling of parse errors (continues processing valid rows)

**Technical Highlights**:
```python
# Robust CSV loading with error recovery
def _load_csv_file(self, filename: str, model_class) -> List[Any]:
    records = []
    with open(file_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row_num, row in enumerate(reader, start=2):
            try:
                record = model_class(**row)
                records.append(record)
            except Exception as e:
                logger.warning(f"Failed to parse row {row_num}: {str(e)}")
                continue  # Continue processing other rows
```

### 2. Policy Data Management (`src/data/policy_data.py`)

**Key Features**:
- Efficient indexing for fast policy lookups by multiple criteria
- Active policy filtering based on Thailand timezone
- Support for complex filtering with multiple parameters
- Integration with payment details, policy details, and benefits
- Exact case-sensitive name matching as per requirements

**Technical Highlights**:
```python
# Multiple index types for different access patterns
self._policy_by_member_code = create_unique_index(policies, "MemberCode")
self._policies_by_insurer_code = create_lookup_index(policies, "InsurerCode")

# Active policy filtering with Thailand timezone
def get_active_policies_by_insurer_code(self, insurer_code: str):
    policies = self._policies_by_insurer_code.get(insurer_code, [])
    return [p for p in policies if is_policy_active(p.get("PlanEffFrom"), p.get("PlanEffTo"))]
```

### 3. Claim Data Management (`src/data/claim_data.py`)

**Key Features**:
- Claim status validation with allowed statuses filtering
- Support for claim lookup by member code, citizen ID, and insurer combinations
- Integration with contract and member conditions
- Automatic filtering of invalid claim statuses with logging
- Comprehensive condition management (contract + member level)

**Technical Highlights**:
```python
# Claim status validation and filtering
valid_claims = []
for claim in claims:
    claim_dict = claim.model_dump()
    if validate_claim_status(claim_dict.get("ClaimStatus", "")):
        valid_claims.append(claim_dict)
    else:
        logger.warning(f"Skipping claim with invalid status: {claim_dict.get('ClaimStatus')}")

# Support for insurer + citizen ID combination lookup
def get_claims_by_insurer_and_citizen(self, insurer_code: str, citizen_id: str):
    insurer_claims = self._claims_by_insurer_code.get(insurer_code, [])
    return [claim for claim in insurer_claims if claim.get("CitizenID") == citizen_id]
```

## Technical Decisions and Rationale

### 1. **Efficient Indexing Strategy**
- **Decision**: Multiple index types (unique vs. lookup) for different access patterns
- **Rationale**: O(1) lookups for member-based queries, O(n) only for complex filtering
- **Benefit**: Fast response times for all three API endpoints

### 2. **Pydantic V2 Migration**
- **Decision**: Updated from deprecated `@validator` to `@field_validator` and `.dict()` to `.model_dump()`
- **Rationale**: Future-proofing and compatibility with latest Pydantic version
- **Benefit**: Eliminates deprecation warnings and ensures long-term maintainability

### 3. **Error Recovery in Data Loading**
- **Decision**: Continue processing when individual rows fail to parse
- **Rationale**: Maximize data availability even with some corrupted records
- **Benefit**: Robust operation in production with imperfect data

### 4. **Claim Status Filtering**
- **Decision**: Filter claims at index-building time rather than query time
- **Rationale**: Better performance and consistent behavior across all claim queries
- **Benefit**: Ensures only valid claims are ever returned

### 5. **Singleton Pattern for Data Managers**
- **Decision**: Global singleton instances for data loader and managers
- **Rationale**: Ensures data is loaded once and shared across the application
- **Benefit**: Memory efficiency and consistent data state

## Data Access Patterns

The Phase 2 implementation supports all required access patterns for the three API endpoints:

### PolicyListSF Endpoint Support:
- Active policy filtering by insurer code
- Multi-criteria filtering (company, policy number, names, etc.)
- Payment details integration
- Exact case-sensitive name matching

### PolicyDetailSF Endpoint Support:
- Policy lookup by member code
- Integration with policy details, benefits, and conditions
- Complete member profile aggregation

### ClaimListSF Endpoint Support:
- Claims by member code
- Claims by insurer code + citizen ID combination
- Valid claim status filtering
- Contract and member conditions integration

## Performance Characteristics

- **Data Loading**: One-time startup cost, lazy loading on first access
- **Policy Lookups**: O(1) for member code, citizen ID lookups
- **Active Policy Filtering**: O(n) where n = policies for specific insurer
- **Claim Lookups**: O(1) for member code, citizen ID lookups
- **Memory Usage**: Multiple indexes trade memory for query performance

## Testing Coverage

Phase 2 includes comprehensive testing:

- **Unit Tests**: 28 test cases covering all major functionality
- **Integration Tests**: 6 test cases with real CSV data
- **Error Handling**: Tests for missing files, parse errors, invalid data
- **Data Consistency**: Cross-manager data validation tests
- **Performance**: Singleton pattern and indexing verification

The Phase 2 implementation provides a robust, efficient, and well-tested data layer that fully supports the requirements for all three API endpoints while maintaining excellent performance characteristics.

## Phase 3 Implementation Summary ✅

### 1. Request Models (`src/models/request_models.py`)

**Key Features**:
- Comprehensive parameter validation for all three API endpoints
- Complex validation logic for PolicyListSF's 10 parameter combinations
- Custom validation using Pydantic V2's `@model_validator` decorator
- Integration with custom exception framework for consistent error handling
- Type-safe field definitions with detailed descriptions

**Technical Highlights**:
```python
# Complex parameter combination validation
@model_validator(mode='after')
def validate_parameter_combinations(self):
    valid_combinations = [
        # 1. INSURER_CODE + CITIZEN_ID
        (self.CITIZEN_ID is not None and
         all(x is None for x in [self.POLICY_NO, self.CERTIFICATE_NO, ...])),
        # ... 9 more combinations
    ]
    if not any(valid_combinations):
        raise ValidationError("Invalid parameter combination...")
```

### 2. Response Models (`src/models/response_models.py`)

**Key Features**:
- Complete response structures for all three API endpoints
- Nested models for complex data structures (PaymentDetail, BenefitDetail, etc.)
- Type-safe field definitions matching API specifications exactly
- Comprehensive field descriptions for API documentation
- Reusable models for shared structures (ClaimHistoryResponse)

**Technical Highlights**:
```python
# Comprehensive response model with nested structures
class PolicyDetailSFResponse(BaseModel):
    ListPolicyDetail: List[PolicyDetailResponse]
    BenefitList: List[BenefitDetailResponse]
    ListContractCondition: List[ContractConditionResponse]
    ListMemberCondition: List[MemberConditionResponse]
    ListClaimHistory: List[ClaimHistoryResponse]

# Type aliases for simpler endpoint responses
PolicyListSFResponse = List[PolicyListResponse]
ClaimListSFResponse = List[ClaimHistoryResponse]
```

## Technical Decisions and Rationale

### 1. **Complex Parameter Validation**
- **Decision**: Use `@model_validator` with custom logic for PolicyListSF combinations
- **Rationale**: Requirements specify 10 specific parameter combinations that cannot be expressed with simple field validation
- **Benefit**: Ensures only valid parameter combinations are accepted, providing clear error messages

### 2. **Custom Exception Integration**
- **Decision**: Raise custom `ValidationError` instead of Pydantic's `ValidationError`
- **Rationale**: Consistent error handling throughout the application with HTTP status code mapping
- **Benefit**: Unified error response format and proper HTTP status codes

### 3. **Nested Response Models**
- **Decision**: Create separate models for each nested structure (PaymentDetail, BenefitDetail, etc.)
- **Rationale**: Better type safety, reusability, and maintainability
- **Benefit**: Clear structure, easy testing, and potential for future API evolution

### 4. **Field Descriptions**
- **Decision**: Add comprehensive descriptions to all fields using Pydantic's `Field` with description
- **Rationale**: Enables automatic API documentation generation and improves developer experience
- **Benefit**: Self-documenting API with clear field meanings

### 5. **Type Aliases for Simple Responses**
- **Decision**: Use type aliases for endpoints that return simple lists
- **Rationale**: Cleaner code while maintaining type safety
- **Benefit**: Simplified response type definitions without losing validation

## API Model Coverage

The Phase 3 implementation provides complete model coverage for all API requirements:

### PolicyListSF Endpoint Support:
- ✅ All 10 parameter combinations validated
- ✅ Complete response structure with payment details
- ✅ Type-safe field mapping from CSV data structures

### PolicyDetailSF Endpoint Support:
- ✅ Simple MEMBER_CODE parameter validation
- ✅ Complex nested response structure with 5 different data types
- ✅ Complete aggregation of policy, benefit, condition, and claim data

### ClaimListSF Endpoint Support:
- ✅ Two parameter combination validation (MEMBER_CODE or INSURER_CODE + CITIZEN_ID)
- ✅ Shared ClaimHistoryResponse model for consistency
- ✅ Complete claim data structure mapping

## Validation Coverage

Phase 3 includes comprehensive validation testing:

- **Unit Tests**: 9 test cases covering all validation scenarios
- **Request Validation**: Tests for valid and invalid parameter combinations
- **Response Creation**: Tests for all response model structures
- **Error Handling**: Tests for proper exception raising and handling
- **Type Safety**: Validation of all field types and nested structures

## Integration Points

The Phase 3 API models provide these integration points for subsequent phases:

### Phase 3 Integration Points:
1. **Request Models**: `PolicyListSFRequest`, `PolicyDetailSFRequest`, `ClaimListSFRequest` for endpoint parameter validation
2. **Response Models**: Complete response structures for JSON serialization
3. **Validation**: Custom validation logic integrated with exception framework
4. **Type Safety**: Full type coverage for request/response data flow
5. **Documentation**: Field descriptions ready for OpenAPI/Swagger generation

### Phase 4 Integration Points:
1. **Validation Service**: `get_validation_service()` for parameter validation and strategy determination
2. **Policy Service**: `get_policy_service()` for PolicyListSF and PolicyDetailSF business logic
3. **Claim Service**: `get_claim_service()` for ClaimListSF business logic
4. **Exception Handling**: Enhanced exception framework with NotFoundError and BusinessLogicError
5. **Service Architecture**: Layered service design with clear separation of concerns

## Next Phase Readiness

Phase 5: API Layer is ready to begin with:
- ✅ Complete business logic implementation for all endpoints
- ✅ Validated request/response models with type safety
- ✅ Service layer with proper error handling and logging
- ✅ Integration with data layer through service abstractions
- ✅ Comprehensive test coverage for all business logic scenarios
- ✅ Clear service interfaces ready for API endpoint integration

The API models provide a complete interface layer that bridges the gap between HTTP requests/responses and the business logic layer, ensuring type safety and validation throughout the application.